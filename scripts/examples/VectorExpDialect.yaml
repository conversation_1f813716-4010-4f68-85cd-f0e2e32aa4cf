- filename: vector-exp-predication.mlir
  type: jit
  arch: general
  commands:
  - exec: ${BUDDY_OPT}
    args: --lower-vector-exp --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-exp-predication-memory.mlir
  type: jit
  arch: rv
  commands:
  - exec: ${BUDDY_OPT}
    args: --lower-vector-exp --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${BUDDY_TRANSLATE}
    args: --buddy-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${QEMU}
    args: -L ${RISCV_GNU_TOOLCHAIN_SYSROOT} -cpu rv64,x-v=true,vlen=128 ${CROSS_LLI} -march=riscv64 -mattr=+m,+d,+v -dlopen=${CROSS_MLIR_C_RUNNER_UTILS} -dlopen=${CROSS_MLIR_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-exp-predication-matmul.mlir
  type: jit
  arch: rv
  commands:
  - exec: ${BUDDY_OPT}
    args: --lower-affine --convert-scf-to-cf --convert-math-to-llvm --lower-vector-exp --lower-rvv --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${BUDDY_TRANSLATE}
    args: --buddy-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${QEMU}
    args: -L ${RISCV_GNU_TOOLCHAIN_SYSROOT} -cpu rv64,x-v=true,vlen=128 ${CROSS_LLI} -march=riscv64 -mattr=+m,+d,+v -dlopen=${CROSS_MLIR_C_RUNNER_UTILS} -dlopen=${CROSS_MLIR_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-exp-add-mask.mlir
  type: jit
  arch: rv
  commands:
  - exec: ${BUDDY_OPT}
    args: --lower-affine --convert-scf-to-cf --convert-math-to-llvm --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${BUDDY_TRANSLATE}
    args: --buddy-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${QEMU}
    args: -L ${RISCV_GNU_TOOLCHAIN_SYSROOT} -cpu rv64,x-v=true,vlen=128 ${CROSS_LLI} -march=riscv64 -mattr=+m,+d,+v -dlopen=${CROSS_MLIR_C_RUNNER_UTILS} -dlopen=${CROSS_MLIR_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-exp-add-predication.mlir
  type: jit
  arch: general
  commands:
  - exec: ${BUDDY_OPT}
    args: --lower-affine --convert-scf-to-cf --convert-math-to-llvm --lower-vector-exp --lower-rvv --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${BUDDY_TRANSLATE}
    args: --buddy-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${QEMU}
    args: '-L ${RISCV_GNU_TOOLCHAIN_SYSROOT} -cpu rv64,x-v=true,vlen=128'
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${CROSS_LLI}
    args: '-march=riscv64 -mattr=+m,+d,+v -dlopen=${CROSS_MLIR_C_RUNNER_UTILS} -dlopen=${CROSS_MLIR_RUNNER_UTILS}'
    workingdir:
    env:
    input: STDIN
    output: STDOUT
