- filename: vector-load.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-scf --lower-affine --convert-scf-to-cf --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env: 
    input: STDIN
    output: STDOUT

- filename: vector-broadcast.mlir
  type: asm
  arch: x86
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_TRANSLATE}
    args: --mlir-to-llvmir
    workingdir:
    env: 
    input: STDIN
    output: STDOUT
  - exec: ${LLC}
    args: ${OPT_FLAG} -mtriple=x86_64-unknown-linux-gnu -mattr=+avx512f
    workingdir:
    env:
    input: STDIN
    output: FILE

- filename: vector-broadcast.mlir
  type: asm
  arch: rv
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_TRANSLATE}
    args: --mlir-to-llvmir
    workingdir:
    env: 
    input: STDIN
    output: STDOUT
  - exec: ${LLC}
    args: ${OPT_FLAG} -mtriple riscv64 -target-abi lp64d -mattr=+m,+d,+v -riscv-v-vector-bits-min=128
    workingdir:
    env:
    input: STDIN
    output: FILE

- filename: vector-broadcast.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-fma.mlir
  type: asm
  arch: x86
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_TRANSLATE}
    args: --mlir-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${LLC}
    args: ${OPT_FLAG} -mtriple=x86_64-unknown-linux-gnu -mattr=+avx512f
    workingdir:
    env:
    input: STDIN
    output: FILE

- filename: vector-fma.mlir
  type: asm
  arch: rv
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_TRANSLATE}
    args: --mlir-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${LLC}
    args: ${OPT_FLAG} -mtriple riscv64 -target-abi lp64d -mattr=+m,+d,+v -riscv-v-vector-bits-min=128
    workingdir:
    env:
    input: STDIN
    output: FILE

- filename: vector-fma.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-long.mlir
  type: asm
  arch: x86
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_TRANSLATE}
    args: --mlir-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${LLC}
    args: ${OPT_FLAG} -mtriple=x86_64-unknown-linux-gnu -mattr=+avx512f
    workingdir:
    env:
    input: STDIN
    output: FILE

- filename: vector-long.mlir
  type: asm
  arch: rv
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_TRANSLATE}
    args: --mlir-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${LLC}
    args: ${OPT_FLAG} -mtriple riscv64 -target-abi lp64d -mattr=+m,+d,+v -riscv-v-vector-bits-min=128
    workingdir:
    env:
    input: STDIN
    output: FILE


- filename: vector-transpose.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-scf --lower-affine --convert-scf-to-cf --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-shape-cast.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-type-cast.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-bitcast.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-scf --lower-affine --convert-scf-to-cf --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-shuffle.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm -split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-splat.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm -split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT
    
- filename: vector-insert.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} --shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-reduction.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} --shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-outerproduct.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} --shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-extract.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} --shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-maskedload.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-maskedstore.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-extract-strided-slice.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 --shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-constant-mask.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-expandload.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-compressstore.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-insert-strided-slice.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  
- filename: vector-scatter.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm -split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-gather.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm -split-input-file -verify-diagnostics --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-transfer-read.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-scf --lower-affine --convert-scf-to-cf --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: vector-transfer-write.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-scf --lower-affine --convert-scf-to-cf --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: FILE

- filename: vector-contract.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-scf --lower-affine --convert-scf-to-cf --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: FILE

- filename: vector-store.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-vector-to-scf --lower-affine --convert-scf-to-cf --convert-vector-to-llvm --convert-memref-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env: 
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=i32 -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: FILE
