- filename: math-atan2.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-math-to-llvm --convert-vector-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void --shared-libs=${MLIR_RUNNER_UTILS} --shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: math-rsqrt.mlir
  type: aot
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: --convert-math-to-llvm --convert-vector-to-llvm --convert-func-to-llvm --reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_TRANSLATE}
    args: --mlir-to-llvmir
    workingdir:
    env:
    input: STDIN
    output: STDOUT
  - exec: ${LLC}
    args: ${OPT_FLAG} -mtriple=x86_64-unknown-linux-gnu -mattr=+avx512f
    workingdir:
    env:
    input: STDIN
    output: FILE
