- filename: tensor-print.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: -arith-bufferize -tensor-bufferize -linalg-bufferize -func-bufferize -buffer-deallocation -convert-linalg-to-loops -convert-linalg-to-llvm -convert-memref-to-llvm -convert-func-to-llvm -reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: tensor-collapse-shape.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: -arith-bufferize -tensor-bufferize -linalg-bufferize -convert-vector-to-llvm -func-bufferize -buffer-deallocation -convert-linalg-to-loops -convert-linalg-to-llvm -convert-memref-to-llvm -convert-func-to-llvm -reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: tensor-extract.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: -arith-bufferize -tensor-bufferize -linalg-bufferize -convert-vector-to-llvm -func-bufferize -buffer-deallocation -convert-linalg-to-loops -convert-linalg-to-llvm -convert-memref-to-llvm -convert-func-to-llvm -reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: tensor-extract-slice.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: -arith-bufferize -tensor-bufferize -linalg-bufferize -convert-vector-to-llvm -func-bufferize -buffer-deallocation -convert-linalg-to-loops -expand-strided-metadata -convert-linalg-to-llvm -convert-memref-to-llvm -convert-func-to-llvm -reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: tensor-from-elements.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: -arith-bufferize -tensor-bufferize -linalg-bufferize -convert-vector-to-llvm -func-bufferize -buffer-deallocation -convert-linalg-to-loops -convert-linalg-to-llvm -convert-memref-to-llvm -convert-func-to-llvm -reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: tensor-insert.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: -arith-bufferize -tensor-bufferize -linalg-bufferize -convert-vector-to-llvm -func-bufferize -buffer-deallocation -convert-linalg-to-loops -convert-linalg-to-llvm -convert-memref-to-llvm -convert-func-to-llvm -reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT

- filename: tensor-insert-slice.mlir
  type: jit
  arch: general
  commands:
  - exec: ${MLIR_OPT}
    args: -arith-bufferize -tensor-bufferize -linalg-bufferize -convert-vector-to-llvm -func-bufferize -buffer-deallocation -convert-linalg-to-loops -expand-strided-metadata -convert-linalg-to-llvm -convert-memref-to-llvm -convert-func-to-llvm -reconcile-unrealized-casts
    workingdir:
    env:
    input: FILE
    output: STDOUT
  - exec: ${MLIR_CPU_RUNNER}
    args: ${OPT_FLAG} -e main -entry-point-result=void -shared-libs=${MLIR_RUNNER_UTILS} -shared-libs=${MLIR_C_RUNNER_UTILS}
    workingdir:
    env:
    input: STDIN
    output: STDOUT
