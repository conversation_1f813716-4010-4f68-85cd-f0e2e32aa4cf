add_mlir_library(BuddyGPUTransformOPs
  TransformOps.cpp

  DEPENDS
  TransformOpsIncGen

  LINK_LIBS PUBLIC
  LLVMSupport
  BuddyGPUUtils
  MLIRAffineDialect
  MLIRArithDialect
  MLIRBufferizationDialect
  MLIRBufferizationTransforms
  MLIRBytecodeWriter
  MLIRFuncDialect
  MLIRFunctionInterfaces
  MLIRGPUDialect
  MLIRGPUTransformOps
  MLIRNVGPUDialect
  MLIRIndexDialect
  MLIRIR
  MLIRLinalgDialect
  MLIRLinalgTransforms
  MLIRLinalgUtils
  MLIRMemRefDialect
  MLIRNVGPUDialect
  MLIRNVGPUTransforms
  MLIRParser
  MLIRPDLDialect
  MLIRPass
  MLIRSCFDialect
  MLIRSideEffectInterfaces
  MLIRTensorTransformOps
  MLIRTransformDialect
  MLIRTransformDialectUtils
  MLIRTransforms
  MLIRVectorDialect
  MLIRVectorToGPU
  MLIRVectorTransforms
  MLIRViewLikeInterface
  MLIRGPUPasses
  )
