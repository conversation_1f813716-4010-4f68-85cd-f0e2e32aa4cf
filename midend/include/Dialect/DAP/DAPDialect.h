//===- DAPDialect.h - dap Dialect Definition --------------------*- C++ -*-===//
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//===----------------------------------------------------------------------===//
//
// This is the header file for the DAP dialect.
//
//===----------------------------------------------------------------------===//

#ifndef DAP_DAPDIALECT_H
#define DAP_DAPDIALECT_H

#include "mlir/IR/Dialect.h"

#include "DAP/DAPOpsDialect.h.inc"

#endif // DAP_DAPDIALECT_H
