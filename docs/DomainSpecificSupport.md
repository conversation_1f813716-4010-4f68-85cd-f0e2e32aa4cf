# Domain-Specific Support

The buddy compiler is a domain-specific compiler infrastructure. We intend to build an ecosystem of domain-specifc applications. This document shows how we support the existing domain frameworks.

## Digital Image Processing

Currently, we support the following framework.

### OpenCV

If you want to install OpenCV on your local device, you can use commands in follow links.

- [install OpenCV in linux](https://docs.opencv.org/4.x/d7/d9f/tutorial_linux_install.html)
- [install OpenCV in windows](https://docs.opencv.org/4.x/d3/d52/tutorial_windows_install.html)

