#projectlogo img {
	padding: 1ex;
}
tt, code, kbd, samp, div.memproto, div.fragment, div.line, table.memname {
	font-family: Consolas, Monaco, Inconsolata, "Courier New", monospace;
}
.image img, .textblock img {
	max-width: 99%;
	max-height: 350px;
}
table.memname, .memname{
	font-weight: bold;
}
code {
	background-color: #EEE;
	padding: 0ex 0.25ex;
}
body {
	margin: 1ex 1ex 0ex 1ex;
	border: 1px solid black;
}
.contents table, .contents div, .contents p, .contents dl {
	font-size: 16px;
	line-height: 1.44;
}
body #nav-tree .label {
	font-size: 14px;
}
a{
	text-decoration: underline;
}
#side-nav {
	margin-left: 1ex;
	border-left: 1px solid black;
}
#nav-tree {
	padding-left: 1ex;
}
#nav-path {
	display: none;
}
div.fragment {
	background-color: #EEE;
	padding: 0.25ex 0.5ex;
	border-color: black;
}
#nav-sync img {
	display: none;
}
