# Check if required plugin is installed
unless Vagrant.has_plugin?("vagrant-disksize")
  raise 'The vagrant-disksize plugin is not installed! Type "vagrant plugin install vagrant-disksize" to install it.'
end

Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/bionic64"
  # needs vagrant plugin install vagrant-disksize:
  config.disksize.size = '40GB'
  config.vm.provider "virtualbox" do |v|
    v.linked_clone = true
    v.cpus = 12
    v.memory = 8192
    v.name = "RISC-V Toolchain (Ubuntu 18)"
  end
  config.vm.provision "shell", inline: <<-SHELL
    apt-get update
    apt-get install -y autoconf automake autotools-dev curl libmpc-dev libmpfr-dev libgmp-dev gawk build-essential bison flex texinfo gperf libtool patchutils bc zlib1g-dev libexpat-dev
    mkdir /opt/riscv
    chown vagrant /opt/riscv
    cd /home/<USER>
    sudu -u vagrant git clone --recursive https://github.com/riscv/riscv-gnu-toolchain
    cd riscv-gnu-toolchain
    sudo -u vagrant ./configure --prefix=/opt/riscv --enable-multilib
    sudo -u vagrant make -j 12 linux
    echo "vagrant provisioner - running tests"
    apt-get install -y expect libglib2.0-dev libfdt-dev libpixman-1-dev zlib1g-dev python
    sudo -u vagrant ./configure --prefix=/opt/riscv
    sudo -u vagrant make report-linux
  SHELL
end
