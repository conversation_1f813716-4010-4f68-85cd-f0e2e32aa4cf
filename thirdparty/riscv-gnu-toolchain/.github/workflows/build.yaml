name: Build

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:     [ubuntu-20.04, ubuntu-22.04]
        mode:   [newlib, linux, musl]
        target: [rv32gc-ilp32d, rv64gc-lp64d]
        compiler: [gcc, llvm]
        exclude:
          - mode: musl
            target: rv32gc-ilp32d
          - mode: musl
            compiler: llvm
    steps:
      - name: Remove unneeded frameworks to recover disk space
        run: |
          echo "-- Before --"
          df -h
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /usr/local/lib/android
          echo "-- After --"
          df -h

      - uses: actions/checkout@v4

      - name: install dependencies
        run: sudo ./.github/setup-apt.sh

      - name: build toolchain
        run: |
          TARGET_TUPLE=($(echo ${{ matrix.target }} | tr "-" "\n"))
          BUILD_TOOLCHAIN="./configure --prefix=/opt/riscv --with-arch=${TARGET_TUPLE[0]} --with-abi=${TARGET_TUPLE[1]}"
          if [ "${{ matrix.compiler }}" == "llvm" ]; then # build toolchain with llvm
            $BUILD_TOOLCHAIN --enable-llvm
          else
            $BUILD_TOOLCHAIN
          fi
          sudo make -j $(nproc) ${{ matrix.mode }}

      - name: make report
        if: |
          matrix.os == 'ubuntu-22.04'
          && (matrix.mode == 'linux' || matrix.mode == 'newlib')
          && matrix.compiler == 'gcc'
        run: |
          sudo make report-${{ matrix.mode }} -j $(nproc)

      - name: recover space
        run: |
          sudo du -hs / 2> /dev/null || true
          sudo rm -rf binutils dejagnu gcc gdb glibc llvm musl newlib pk qemu spike || true
          sudo du -hs / 2> /dev/null || true

      - name: tarball build
        run: tar czvf riscv.tar.gz -C /opt/ riscv/

      - name: generate prebuilt toolchain name
        id:   toolchain-name-generator
        run: |
          if [[ "${{ matrix.target }}" == *"32"* ]]; then BITS=32; else BITS=64; fi
          case "${{ matrix.mode }}" in
            "linux")
              MODE="glibc";;
            "musl")
              MODE="musl";;
            *)
              MODE="elf";;
          esac
          echo "TOOLCHAIN_NAME=riscv$BITS-$MODE-${{ matrix.os }}-${{ matrix.compiler }}-nightly" >> $GITHUB_OUTPUT

      - uses: actions/upload-artifact@v3
        with:
          name: ${{ steps.toolchain-name-generator.outputs.TOOLCHAIN_NAME }}
          path: riscv.tar.gz

  test-sim:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:     [ubuntu-22.04]
        mode:   [newlib]
        target: [rv64gc-lp64d]
        sim:    [spike]
    steps:
      - name: Remove unneeded frameworks to recover disk space
        run: |
          echo "-- Before --"
          df -h
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /usr/local/lib/android
          echo "-- After --"
          df -h

      - uses: actions/checkout@v4

      - name: install dependencies
        run: sudo ./.github/setup-apt.sh

      - name: build toolchain
        run: |
          TARGET_TUPLE=($(echo ${{ matrix.target }} | tr "-" "\n"))
          ./configure --prefix=/opt/riscv --with-arch=${TARGET_TUPLE[0]} --with-abi=${TARGET_TUPLE[1]} --with-sim=${{ matrix.sim }}
          make -j $(nproc) ${{ matrix.mode }}

      - name: make report
        run: make report-${{ matrix.mode }} -j $(nproc)

  build-multilib:
    if: ${{ false }} # Disable until multilib errors are triaged
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os:     [ubuntu-22.04]
        mode:   [newlib, linux]
        target: [rv64gc-lp64d]
    steps:
      - name: Remove unneeded frameworks to recover disk space
        run: |
          echo "-- Before --"
          df -h
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /usr/local/lib/android
          echo "-- After --"
          df -h

      - uses: actions/checkout@v4

      - name: install dependencies
        run: sudo ./.github/setup-apt.sh

      - name: build toolchain
        run: |
          TARGET_TUPLE=($(echo ${{ matrix.target }} | tr "-" "\n"))
          ./configure --prefix=/opt/riscv --with-arch=${TARGET_TUPLE[0]} --with-abi=${TARGET_TUPLE[1]} --enable-multilib
          sudo make -j $(nproc) ${{ matrix.mode }}

      - name: make report
        run: |
          sudo make report-${{ matrix.mode }} -j $(nproc)

      - name: tarball build
        run: tar czvf riscv.tar.gz -C /opt/ riscv/

      - name: generate prebuilt toolchain name
        id:   toolchain-name-generator
        run: |
          if [[ "${{ matrix.target }}" == *"32"* ]]; then BITS=32; else BITS=64; fi
          case "${{ matrix.mode }}" in
            "linux")
              MODE="glibc";;
            "musl")
              MODE="musl";;
            *)
              MODE="elf";;
          esac
          echo "TOOLCHAIN_NAME=riscv$BITS-$MODE-${{ matrix.os }}-multilib-nightly" >> $GITHUB_OUTPUT

      - uses: actions/upload-artifact@v3
        with:
          name: ${{ steps.toolchain-name-generator.outputs.TOOLCHAIN_NAME }}
          path: riscv.tar.gz
