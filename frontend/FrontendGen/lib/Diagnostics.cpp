//====- Diagnostics.cpp -------------------------------------------------===//
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
//===----------------------------------------------------------------------===//

#include "Diagnostics.h"
#include "llvm/Support/SourceMgr.h"

using namespace frontendgen;
namespace {

/// Storage the message of the diagnostic.
const char *diagnosticText[] = {
#define DIAG(ID, Level, Msg) Msg,
#include "Diagnostics.def"
};

/// Storage the kind of the diagnostic.
llvm::SourceMgr::DiagKind diagnosticKind[] = {
#define DIAG(ID, Level, Msg) llvm::SourceMgr::DK_##Level,
#include "Diagnostics.def"
};
} // namespace

/// Get the message of the diagnostic.
const char *DiagnosticEngine::getDiagnosticText(unsigned diagID) {
  return diagnosticText[diagID];
}
/// Get the kind of the diagnostic.
llvm::SourceMgr::DiagKind DiagnosticEngine::getDiagnosticKind(unsigned DiagID) {
  return diagnosticKind[DiagID];
}
