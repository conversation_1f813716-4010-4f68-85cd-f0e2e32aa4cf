#ifndef terminator
#define terminator(NAME)
#endif

terminator(Var, 'var')
terminator(Add, 'add')
terminator(Sub, 'sub')
terminator(Def, 'def')
terminator(Return, 'return')
terminator(ParentheseOpen, '(')
terminator(ParentheseClose, ')')
terminator(Comma, ',')
terminator(BracketOpen, '{')
terminator(BracketClose, '}')
terminator(SbracketOpen, '[')
terminator(SbracketClose, ']')
terminator(Semi, ';')
terminator(AngleBracketOpen, '<')
terminator(AngleBracketClose, '>')
terminator(Number, [0-9]+)
terminator(Equal, '=')
#undef terminator
