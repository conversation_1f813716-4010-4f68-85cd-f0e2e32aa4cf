#ifndef CPPMAP
#define CPPMAP(key, value)
#endif

#ifndef ARGUMENTSMAP
#define ARGUMENTSMAP(key, value)
#endif

#ifndef RESULTSMAP
#define RESULTSMAP(key, value)
#endif

CPPMAP("\"StringRef\"", "llvm::StringRef")
CPPMAP("\"ArrayRef<Value>\"", "llvm::ArrayRef<Value>")
CPPMAP("\"FunctionType\"", "llvm::FunctionType")
CPPMAP("\"ArrayRef<NamedAttribute>\"", "llvm::ArrayRef<NamedAttribute>")
CPPMAP("\"Value\"", "mlir::Value")
CPPMAP("\"double\"", "double")
CPPMAP("\"DenseElementsAttr\"", "mlir::DenseElementsAttr")

ARGUMENTSMAP("F64ElementsAttr", "mlir::Value")
ARGUMENTSMAP("F64Tensor", "mlir::Value")
ARGUMENTSMAP("Variadic<F64Tensor>", "mlir::Value")
ARGUMENTSMAP("SymbolNameAttr", "llvm::StringRef")
ARGUMENTSMAP("TypeAttrOf<FunctionType>", "mlir::FunctionType")
ARGUMENTSMAP("F64MemRef", "mlir::Value")
RESULTSMAP("StaticShapeTensorOf<[F64]>", "mlir::Type")
RESULTSMAP("F64Tensor", "mlir::Type")

#undef TYPEMAP
#undef ARGUMENTSMAP
#undef RESULTSMAP
